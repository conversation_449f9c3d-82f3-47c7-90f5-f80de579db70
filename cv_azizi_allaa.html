<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CV - AZIZI ALLAA</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #2c3e50;
            background-color: #f8f9fa;
        }

        .container {
            max-width: 210mm;
            margin: 0 auto;
            background: white;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
            min-height: 297mm;
        }

        .header {
            background: linear-gradient(135deg, #2980b9, #3498db);
            color: white;
            padding: 40px 30px;
            text-align: center;
            position: relative;
        }

        .header h1 {
            font-size: 3em;
            font-weight: 700;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .header h2 {
            font-size: 1.5em;
            font-weight: 300;
            margin-bottom: 5px;
        }

        .header p {
            font-size: 1.1em;
            opacity: 0.9;
        }

        .content {
            display: flex;
            min-height: calc(297mm - 160px);
        }

        .sidebar {
            background: #ecf0f1;
            width: 35%;
            padding: 30px 25px;
        }

        .main-content {
            width: 65%;
            padding: 30px 35px;
        }

        .section-title {
            color: #2980b9;
            font-size: 1.3em;
            font-weight: 700;
            margin-bottom: 15px;
            padding-bottom: 8px;
            border-bottom: 3px solid #3498db;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .section-title i {
            color: #e74c3c;
        }

        .info-item {
            margin-bottom: 15px;
        }

        .info-label {
            font-weight: 600;
            color: #34495e;
            margin-bottom: 3px;
        }

        .info-value {
            color: #2c3e50;
        }

        .highlight {
            color: #e74c3c;
            font-weight: 600;
        }

        .experience-item {
            margin-bottom: 25px;
            padding: 20px;
            background: #f8f9fa;
            border-left: 4px solid #3498db;
            border-radius: 5px;
        }

        .experience-title {
            color: #2980b9;
            font-size: 1.2em;
            font-weight: 600;
            margin-bottom: 8px;
        }

        .experience-details {
            margin-bottom: 10px;
        }

        .experience-details span {
            color: #7f8c8d;
            font-weight: 500;
        }

        .responsibilities {
            list-style: none;
            padding-left: 0;
        }

        .responsibilities li {
            margin-bottom: 5px;
            padding-left: 20px;
            position: relative;
        }

        .responsibilities li:before {
            content: "▶";
            color: #e74c3c;
            position: absolute;
            left: 0;
        }

        .qualities {
            list-style: none;
            padding-left: 0;
        }

        .qualities li {
            margin-bottom: 8px;
            padding: 8px 15px;
            background: white;
            border-radius: 20px;
            display: flex;
            align-items: center;
            gap: 10px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }

        .qualities li i {
            color: #27ae60;
        }

        .objectives {
            list-style: none;
            padding-left: 0;
        }

        .objectives li {
            margin-bottom: 10px;
            padding-left: 25px;
            position: relative;
        }

        .objectives li:before {
            content: "🎯";
            position: absolute;
            left: 0;
        }

        .footer {
            background: #ecf0f1;
            padding: 20px;
            text-align: center;
            color: #34495e;
            font-weight: 600;
            border-top: 3px solid #3498db;
        }

        @media print {
            body {
                background: white;
            }
            .container {
                box-shadow: none;
                max-width: none;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>AZIZI ALLAA</h1>
        </div>

        <div class="content">
            <div class="sidebar">
                <div class="section-title">
                    <i class="fas fa-user"></i>
                    INFORMATIONS PERSONNELLES
                </div>
                
                <div class="info-item">
                    <div class="info-label">Date de naissance:</div>
                    <div class="info-value">05 décembre 1993 (Sétif)</div>
                </div>
                
                <div class="info-item">
                    <div class="info-label">Situation matrimoniale:</div>
                    <div class="info-value">Célibataire</div>
                </div>
                
                <div class="info-item">
                    <div class="info-label">Nationalité:</div>
                    <div class="info-value">Algérienne</div>
                </div>
                
                <div class="info-item">
                    <div class="info-label">Adresse:</div>
                    <div class="info-value">Cité 1200 logements<br>Sétif, Algérie</div>
                </div>
                
                <div class="info-item">
                    <div class="info-label">Téléphone:</div>
                    <div class="info-value">
                        <i class="fas fa-phone"></i> 0771 39 49 24<br>
                        <i class="fas fa-phone"></i> 0563 83 26 41
                    </div>
                </div>
                
                <div class="info-item">
                    <div class="info-label">Permis de conduire:</div>
                    <div class="info-value"><i class="fas fa-car"></i> Catégorie B</div>
                </div>
                
                <div class="info-item">
                    <div class="info-label">Service militaire:</div>
                    <div class="info-value">Exempté</div>
                </div>

                <div class="section-title">
                    <i class="fas fa-language"></i>
                    LANGUES
                </div>
                
                <div class="info-item">
                    <div class="info-label">Arabe:</div>
                    <div class="info-value"><span class="highlight">Courant</span><br>(Parlé, Lu, Écrit)</div>
                </div>
                
                <div class="info-item">
                    <div class="info-label">Français:</div>
                    <div class="info-value"><span class="highlight">Intermédiaire</span><br>(Parlé, Lu, Écrit)</div>
                </div>

                <div class="section-title">
                    <i class="fas fa-star"></i>
                    QUALITÉS PROFESSIONNELLES
                </div>
                
                <ul class="qualities">
                    <li><i class="fas fa-check-circle"></i> <strong>Sérieux</strong></li>
                    <li><i class="fas fa-check-circle"></i> <strong>Motivé</strong></li>
                    <li><i class="fas fa-check-circle"></i> <strong>Ponctuel</strong></li>
                    <li><i class="fas fa-check-circle"></i> <strong>Dynamique</strong></li>
                    <li><i class="fas fa-check-circle"></i> <strong>Organisé</strong></li>
                </ul>
            </div>

            <div class="main-content">
                <div class="section-title">
                    <i class="fas fa-briefcase"></i>
                    EXPÉRIENCE PROFESSIONNELLE
                </div>

                <div class="experience-item">
                    <div class="experience-title">Haddadji HB Molding</div>
                    <div class="experience-details">
                        <span>Poste:</span> <span class="highlight">Opérateur</span>
                    </div>
                </div>

                <div class="experience-item">
                    <div class="experience-title">Chiali Trading</div>
                    <div class="experience-details">
                        <span>Poste:</span> <span class="highlight">Manutentionnaire</span>
                    </div>
                </div>

                <div class="experience-item">
                    <div class="experience-title">SOFAF</div>
                    <div class="experience-details">
                        <span>Poste:</span> <span class="highlight">Opérateur</span>
                    </div>
                </div>
            </div>
        </div>


    </div>
</body>
</html>
