\documentclass[11pt,a4paper]{article}

% Packages
\usepackage[utf8]{inputenc}
\usepackage[T1]{fontenc}
\usepackage[french]{babel}
\usepackage{geometry}
\usepackage{xcolor}
\usepackage{fontawesome}
\usepackage{titlesec}
\usepackage{enumitem}
\usepackage{tabularx}
\usepackage{array}
\usepackage{multirow}
\usepackage{tikz}
\usepackage{graphicx}
\usepackage{hyperref}

% Geometry
\geometry{left=1.5cm, right=1.5cm, top=1.5cm, bottom=1.5cm}

% Colors
\definecolor{primarycolor}{RGB}{41, 128, 185}
\definecolor{secondarycolor}{RGB}{52, 73, 94}
\definecolor{accentcolor}{RGB}{231, 76, 60}
\definecolor{lightgray}{RGB}{236, 240, 241}
\definecolor{darkgray}{RGB}{149, 165, 166}

% Custom commands
\newcommand{\sectiontitle}[1]{%
    \vspace{0.5em}
    {\color{primarycolor}\Large\bfseries #1}
    \vspace{0.2em}
    \hrule height 2pt
    \vspace{0.5em}
}

\newcommand{\subsectiontitle}[1]{%
    \vspace{0.3em}
    {\color{secondarycolor}\large\bfseries #1}
    \vspace{0.2em}
}

\newcommand{\highlight}[1]{%
    {\color{accentcolor}\textbf{#1}}
}

% Remove page numbering
\pagestyle{empty}

% Hyperref setup
\hypersetup{
    colorlinks=true,
    linkcolor=primarycolor,
    urlcolor=primarycolor,
    pdftitle={CV - AZIZI ALLAA},
    pdfauthor={AZIZI ALLAA}
}

\begin{document}

% Header
\begin{center}
    \begin{tikzpicture}
        \fill[primarycolor] (0,0) rectangle (\textwidth,2);
        \node[white,font=\Huge\bfseries] at (\textwidth/2,1) {AZIZI ALLAA};
    \end{tikzpicture}
\end{center}

\vspace{0.5em}

% Main content in two columns
\begin{minipage}[t]{0.5\textwidth}
    
    % Contact Information
    \sectiontitle{\faUser\ INFORMATIONS PERSONNELLES}
    
    \textbf{\color{secondarycolor}Date de naissance:}\\
    05 décembre 1993 (Sétif)
    
    \vspace{0.3em}
    \textbf{\color{secondarycolor}Situation matrimoniale:}\\
    Célibataire
    
    \vspace{0.3em}
    \textbf{\color{secondarycolor}Nationalité:}\\
    Algérienne
    
    \vspace{0.3em}
    \textbf{\color{secondarycolor}Adresse:}\\
    Cité 1200 logements\\
    Sétif, Algérie
    
    \vspace{0.3em}
    \textbf{\color{secondarycolor}Téléphone:}\\
    \faPhone\ 0771 39 49 24\\
    \faPhone\ 0563 83 26 41
    
    \vspace{0.3em}
    \textbf{\color{secondarycolor}Permis de conduire:}\\
    \faCar\ Catégorie B
    
    \vspace{0.3em}
    \textbf{\color{secondarycolor}Service militaire:}\\
    Exempté
    
    \vspace{1em}
    
    % Languages
    \sectiontitle{\faLanguage\ LANGUES}
    
    \textbf{\color{secondarycolor}Arabe:}\\
    \highlight{Courant}\\
    (Parlé, Lu, Écrit)
    
    \vspace{0.5em}
    \textbf{\color{secondarycolor}Français:}\\
    \highlight{Intermédiaire}\\
    (Parlé, Lu, Écrit)
    

    
\end{minipage}
\hfill
\begin{minipage}[t]{0.45\textwidth}
    
    % Professional Experience
    \sectiontitle{\faBriefcase\ EXPÉRIENCE PROFESSIONNELLE}
    
    \subsectiontitle{Haddadji HB Molding}
    \textbf{\color{darkgray}Poste:} \highlight{Opérateur}

    \vspace{0.5em}

    \subsectiontitle{Chiali Trading}
    \textbf{\color{darkgray}Poste:} \highlight{Manutentionnaire}

    \vspace{0.5em}

    \subsectiontitle{SOFAF}
    \textbf{\color{darkgray}Poste:} \highlight{Opérateur}

    \vspace{1em}

    % Professional Qualities
    \sectiontitle{\faStar\ QUALITÉS PROFESSIONNELLES}

    \begin{itemize}[leftmargin=1em, itemsep=0.2em]
        \item[\color{accentcolor}\faCheck] \textbf{Sérieux}
        \item[\color{accentcolor}\faCheck] \textbf{Motivé}
        \item[\color{accentcolor}\faCheck] \textbf{Ponctuel}
        \item[\color{accentcolor}\faCheck] \textbf{Dynamique}
        \item[\color{accentcolor}\faCheck] \textbf{Organisé}
    \end{itemize}

\end{minipage}

\vspace{1em}



\end{document}
